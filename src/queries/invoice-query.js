/**
 * Invoice Query Functions
 * SQL queries for invoice-related data retrieval
 */

module.exports = {
  /**
   * Query to get patient history records by patient ID
   * @param {string} patientId - The patient ID
   * @returns {string} SQL query string
   */
  getPatientHistoryByPatientIdQuery: (patientId) => {
    return `SELECT * FROM c WHERE c.patientId = '${patientId}' ORDER BY c.updated_on DESC`
  },

  /**
   * Query to get patient history records with CONTAINS for partial match
   * @param {string} patientId - The patient ID
   * @returns {string} SQL query string
   */
  getPatientHistoryByPatientIdContainsQuery: (patientId) => {
    return `SELECT * FROM c WHERE c.patientId = '${patientId}' OR CONTAINS(c.patientId, '${patientId}') ORDER BY c.updated_on DESC`
  },

  /**
   * Query to get patient consultations by patient ID
   * @param {string} patientId - The patient ID
   * @returns {string} SQL query string
   */
  getPatientConsultationsByPatientIdQuery: (patientId) => {
    return `SELECT * FROM c WHERE c.patientId = '${patientId}' ORDER BY c.updated_on DESC`
  },

  /**
   * Query to get lab test details from standard lab_tests container
   * @param {string} testId - The test ID
   * @returns {string} SQL query string
   */
  getLabTestDetailsQuery: (testId) => {
    return `SELECT c.CLASS, c.LONG_COMMON_NAME, c.DisplayName, c.SHORTNAME FROM c WHERE c.id = "${testId}"`
  },

  /**
   * Query to get organization-specific test details
   * @param {string} testId - The test ID
   * @param {string} organizationId - The organization ID
   * @returns {string} SQL query string
   */
  getOrganizationTestDetailsQuery: (testId, organizationId) => {
    return `SELECT c.CLASS, c.testName, c.price, c.DisplayName FROM c WHERE c.testId = "${testId}" AND c.organizationId = "${organizationId}"`
  },
}
