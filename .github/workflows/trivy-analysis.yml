name: Trivy Security Scan

on:
  push:
    branches: [ "main", "staging", "develop" ]
  pull_request:
    branches: [ "main", "staging", "develop" ]
  workflow_dispatch:

permissions:
  contents: read
  security-events: write # For uploading SARIF results (even if Code Scanning is disabled)
  actions: read # For workflow run access
  # FIX FOR 403 ERROR: Grants GITHUB_TOKEN permission to post PR comments
  issues: write 

jobs:
  trivy-scan:
    name: <PERSON><PERSON> and <PERSON>an
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set Environment Variables
        env:
          ACR_LOGIN_SERVER_DEV: ${{ secrets.ACR_LOGIN_SERVER_DEV }}
          ACR_USERNAME_DEV: ${{ secrets.ACR_USERNAME_DEV }}
          ACR_PASSWORD_DEV: ${{ secrets.ACR_PASSWORD_DEV }}
          ACR_LOGIN_SERVER_STAGING: ${{ secrets.ACR_LOGIN_SERVER_STAGING }}
          ACR_USERNAME_STAGING: ${{ secrets.ACR_USERNAME_STAGING }}
          ACR_PASSWORD_STAGING: ${{ secrets.ACR_PASSWORD_STAGING }}
        run: |
          if [[ "${{ github.ref_name }}" == "staging" ]]; then
            echo "ACR_LOGIN_SERVER=$ACR_LOGIN_SERVER_STAGING" >> $GITHUB_ENV
            echo "ACR_USERNAME=$ACR_USERNAME_STAGING" >> $GITHUB_ENV
            echo "ACR_PASSWORD=$ACR_PASSWORD_STAGING" >> $GITHUB_ENV
          else
            echo "ACR_LOGIN_SERVER=$ACR_LOGIN_SERVER_DEV" >> $GITHUB_ENV
            echo "ACR_USERNAME=$ACR_USERNAME_DEV" >> $GITHUB_ENV
            echo "ACR_PASSWORD=$ACR_PASSWORD_DEV" >> $GITHUB_ENV
          fi

      - name: Log in to Azure Container Registry
        id: acr-login
        uses: docker/login-action@v3
        continue-on-error: true
        with:
          registry: ${{ env.ACR_LOGIN_SERVER }}
          username: ${{ env.ACR_USERNAME }}
          password: ${{ env.ACR_PASSWORD }}

      - name: Check ACR Login Status
        run: |
          if [[ "${{ steps.acr-login.outcome }}" == "failure" ]]; then
            echo "⚠️ ACR login failed. Image scanning will be skipped."
            echo "Please check your Azure Container Registry credentials in GitHub secrets:"
            echo "- ACR_LOGIN_SERVER_DEV/ACR_LOGIN_SERVER_STAGING"
            echo "- ACR_USERNAME_DEV/ACR_USERNAME_STAGING"
            echo "- ACR_PASSWORD_DEV/ACR_PASSWORD_STAGING"
          else
            echo "✅ ACR login successful"
          fi

      - name: Build Docker Image
        id: build-image
        if: steps.acr-login.outcome == 'success'
        run: |
          # Sanitize branch name for Docker tag (replace / with -)
          BRANCH_NAME="${{ github.ref_name }}"
          SANITIZED_BRANCH=$(echo "$BRANCH_NAME" | sed 's/\//-/g' | sed 's/[^a-zA-Z0-9._-]/-/g')
          IMAGE_TAG=${{ env.ACR_LOGIN_SERVER }}/emr-ms:${SANITIZED_BRANCH}-${{ github.sha }}
          echo "Building image with tag: $IMAGE_TAG"
          docker build -t $IMAGE_TAG .
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV

      # 1. RUN FILESYSTEM SCAN (SARIF output for archiving)
      - name: Run Trivy Vulnerability Scanner (Filesystem)
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-fs-results.sarif'
          severity: 'CRITICAL,HIGH'

      # 2. GENERATE PR COMMENT TABLE (Text output for human-readability)
      - name: Generate PR Comment Table (Filesystem)
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'
          output: 'trivy-fs-comment.txt' 
          severity: 'CRITICAL,HIGH'

      # 3. POST PR COMMENT 
      - name: Comment on PR with Filesystem Scan Results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const results = fs.readFileSync('trivy-fs-comment.txt', 'utf8');
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: "### 🛡️ Trivy Filesystem Scan Results\n\n```\n" + results + "\n```"
            })

      # 4. RUN SBOM GENERATION
      - name: Run Trivy SBOM Generation
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          format: 'cyclonedx'
          output: 'sbom-${{ github.sha }}.json'

      # 5. RUN IMAGE SCAN
      - name: Run Trivy Vulnerability Scanner (Image)
        uses: aquasecurity/trivy-action@master
        if: steps.build-image.outcome == 'success'
        with:
          scan-type: 'image'
          image-ref: ${{ env.IMAGE_TAG }}
          format: 'sarif'
          output: 'trivy-image-results.sarif'
          severity: 'CRITICAL,HIGH'
          ignore-unfixed: true

      # Upload SARIF to GitHub (even if UI is disabled, this is good practice)
      - name: Upload Trivy Scan Results (FS)
        uses: github/codeql-action/upload-sarif@v4
        continue-on-error: true
        if: always() && hashFiles('trivy-fs-results.sarif') != ''
        with:
          sarif_file: 'trivy-fs-results.sarif'
          category: 'trivy-fs'

      - name: Upload Trivy Scan Results (Image)
        uses: github/codeql-action/upload-sarif@v4
        continue-on-error: true
        if: always() && hashFiles('trivy-image-results.sarif') != ''
        with:
          sarif_file: 'trivy-image-results.sarif'
          category: 'trivy-image'

      # Standard Artifact Uploads (short-term storage for manual download)
      - name: Upload Security Scan Results as Artifacts
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: trivy-security-results-${{ github.sha }}
          path: |
            trivy-fs-results.sarif
            trivy-image-results.sarif
            sbom-${{ github.sha }}.json
          retention-days: 30
          if-no-files-found: ignore

      - name: Upload SBOM Artifact
        uses: actions/upload-artifact@v4
        if: hashFiles('sbom-${{ github.sha }}.json') != ''
        with:
          name: sbom-${{ github.sha }}
          path: sbom-${{ github.sha }}.json
          retention-days: 90

      # --- START AZURE BLOB STORAGE ARCHIVING ---
      - name: Azure Login for Blob Upload
        uses: azure/login@v1
        if: success()
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}
          
      - name: Upload Security Reports to Azure Blob Storage
        uses: azure/CLI@v1
        if: success()
        with:
          inlineScript: |
            # Define container and local files
            CONTAINER_NAME="trivy-security-audit"
            STORAGE_ACCOUNT="${{ secrets.AZURE_STORAGE_ACCOUNT }}"
            COMMIT_SHA="${{ github.sha }}"

            # Create container if it does not exist (Requires proper permissions in AZURE_CREDENTIALS)
            az storage container create --account-name $STORAGE_ACCOUNT --name $CONTAINER_NAME --auth-mode login --fail-on-exist || true

            echo "Uploading artifacts to $CONTAINER_NAME/$COMMIT_SHA/"

            # Upload Filesystem SARIF
            if [[ -f "trivy-fs-results.sarif" ]]; then
              az storage blob upload \
                --account-name $STORAGE_ACCOUNT \
                --container-name $CONTAINER_NAME \
                --name "$COMMIT_SHA/trivy-fs-results.sarif" \
                --file "trivy-fs-results.sarif" \
                --auth-mode login
            fi

            # Upload Image SARIF
            if [[ -f "trivy-image-results.sarif" ]]; then
              az storage blob upload \
                --account-name $STORAGE_ACCOUNT \
                --container-name $CONTAINER_NAME \
                --name "$COMMIT_SHA/trivy-image-results.sarif" \
                --file "trivy-image-results.sarif" \
                --auth-mode login
            fi

            # Upload SBOM
            if [[ -f "sbom-$COMMIT_SHA.json" ]]; then
              az storage blob upload \
                --account-name $STORAGE_ACCOUNT \
                --container-name $CONTAINER_NAME \
                --name "$COMMIT_SHA/sbom.json" \
                --file "sbom-$COMMIT_SHA.json" \
                --auth-mode login
            fi
      # --- END AZURE BLOB STORAGE ARCHIVING ---

      - name: Security Scan Summary
        if: always()
        run: |
          echo "## 🔒 Security Scan Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [[ -f "trivy-fs-results.sarif" ]]; then
            echo "✅ Filesystem scan completed (Results posted to PR comments)" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Filesystem scan failed" >> $GITHUB_STEP_SUMMARY
          fi

          if [[ -f "trivy-image-results.sarif" ]]; then
            echo "✅ Container image scan completed" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ Container image scan skipped (ACR login failed or image build failed)" >> $GITHUB_STEP_SUMMARY
          fi

          if [[ -f "sbom-${{ github.sha }}.json" ]]; then
            echo "✅ SBOM (Software Bill of Materials) generated (Archived to Azure)" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ SBOM generation failed" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY
          echo "📁 Security scan results are available as workflow artifacts for download." >> $GITHUB_STEP_SUMMARY